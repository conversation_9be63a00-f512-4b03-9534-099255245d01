// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    // NOTE: When using mysql or sqlserver, uncomment the @db.Text annotations in model Account below
    // Further reading:
    // https://next-auth.js.org/adapters/prisma#create-the-prisma-schema
    // https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string
    url      = env("DATABASE_URL")
}

// Game Models

model Player {
    id                String   @id @default(cuid())
    userId            String   @unique
    user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    
    // Core currencies
    cryptoCoin        Decimal  @default(0) @db.Decimal(20, 8)
    ethereumG         Decimal  @default(0) @db.Decimal(20, 8)
    byteCoin          Decimal  @default(0) @db.Decimal(20, 8)
    solanaX           Decimal  @default(0) @db.Decimal(20, 8)
    cardanoZ          Decimal  @default(0) @db.Decimal(20, 8)
    polkadotY         Decimal  @default(0) @db.Decimal(20, 8)
    
    // Game progression
    totalClicks       BigInt   @default(0)
    prestigeLevel     Int      @default(0)
    blockchainPoints  Int      @default(0)
    
    // Timestamps
    lastActiveAt      DateTime @default(now())
    createdAt         DateTime @default(now())
    updatedAt         DateTime @updatedAt
    
    // Relations
    miningRigs        MiningRig[]
    shards           PlayerShard[]
    research         PlayerResearch[]
    dataCenters      PlayerDataCenter[]
    buyerTrades      Trade[] @relation("BuyerTrades")
    sellerTrades     Trade[] @relation("SellerTrades")
    tradeListings    TradeListings[]
    syndicateMember  SyndicateMember?
    daoVotes         DAOVote[]
    daoProposals     DAOProposal[]
    syndicateLeader  Syndicate?
    currencySettings PlayerCurrencySettings[]

    @@index([userId])
    @@index([lastActiveAt])
}

model MiningRig {
    id          String   @id @default(cuid())
    playerId    String
    player      Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)
    
    rigType     RigType
    level       Int      @default(1)
    quantity    Int      @default(1)
    
    // Performance stats
    baseOutput  Decimal  @db.Decimal(20, 8)
    efficiency  Decimal  @default(1.0) @db.Decimal(10, 4)
    
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    @@index([playerId])
    @@index([rigType])
}

model BlockchainShard {
    id          String      @id @default(cuid())
    name        String      @unique
    rarity      ShardRarity
    category    ShardCategory
    effects     Json        // Stored as JSON for flexibility
    description String      @db.Text
    
    playerShards PlayerShard[]
    tradeListings TradeListings[]

    @@index([rarity])
    @@index([category])
}

model PlayerShard {
    id       String @id @default(cuid())
    playerId String
    player   Player @relation(fields: [playerId], references: [id], onDelete: Cascade)
    shardId  String
    shard    BlockchainShard @relation(fields: [shardId], references: [id])
    
    quantity Int @default(1)
    equipped Boolean @default(false)
    
    acquiredAt DateTime @default(now())

    @@index([playerId])
    @@index([shardId])
    @@index([equipped])
}

model PlayerResearch {
    id          String   @id @default(cuid())
    playerId    String
    player      Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)
    
    researchId  String
    level       Int      @default(1)
    isCompleted Boolean  @default(false)
    
    startedAt   DateTime @default(now())
    completedAt DateTime?

    @@unique([playerId, researchId])
    @@index([playerId])
}

model PlayerDataCenter {
    id          String   @id @default(cuid())
    playerId    String
    player      Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)
    
    locationId  String
    level       Int      @default(1)
    isActive    Boolean  @default(true)
    
    establishedAt DateTime @default(now())

    @@unique([playerId, locationId])
    @@index([playerId])
}

// Market and Trading Models

model CryptocurrencyPrice {
    id           String          @id @default(cuid())
    currency     CurrencyType
    price        Decimal         @db.Decimal(20, 8)
    volume24h    Decimal         @db.Decimal(20, 8)
    volatility   Decimal         @db.Decimal(10, 4)
    
    timestamp    DateTime        @default(now())

    @@index([currency])
    @@index([timestamp])
}

model CurrencySettings {
    id              String       @id @default(cuid())
    currency        CurrencyType @unique
    name            String
    symbol          String
    color           String
    description     String       @db.Text
    baseValue       Decimal      @db.Decimal(20, 8)
    isEnabled       Boolean      @default(true)
    isUnlocked      Boolean      @default(false)
    unlockRequirement Json?      // Stored as JSON for flexibility
    
    createdAt       DateTime     @default(now())
    updatedAt       DateTime     @updatedAt

    @@index([isEnabled])
    @@index([isUnlocked])
}

model PlayerCurrencySettings {
    id              String       @id @default(cuid())
    playerId        String
    player          Player       @relation(fields: [playerId], references: [id], onDelete: Cascade)
    currency        CurrencyType
    isUnlocked      Boolean      @default(false)
    isMiningTarget  Boolean      @default(false)
    lastMined       DateTime?
    
    createdAt       DateTime     @default(now())
    updatedAt       DateTime     @updatedAt

    @@unique([playerId, currency])
    @@index([playerId])
    @@index([currency])
    @@index([isMiningTarget])
}

model TradeListings {
    id          String   @id @default(cuid())
    sellerId    String
    seller      Player   @relation(fields: [sellerId], references: [id], onDelete: Cascade)
    shardId     String
    shard       BlockchainShard @relation(fields: [shardId], references: [id])
    
    price       Decimal  @db.Decimal(20, 8)
    currency    CurrencyType
    quantity    Int
    
    isActive    Boolean  @default(true)
    createdAt   DateTime @default(now())
    expiresAt   DateTime?

    @@index([sellerId])
    @@index([shardId])
    @@index([isActive])
    @@index([createdAt])
}

model Trade {
    id          String   @id @default(cuid())
    buyerId     String
    buyer       Player   @relation("BuyerTrades", fields: [buyerId], references: [id])
    sellerId    String
    seller      Player   @relation("SellerTrades", fields: [sellerId], references: [id])
    
    shardId     String
    price       Decimal  @db.Decimal(20, 8)
    currency    CurrencyType
    quantity    Int
    
    completedAt DateTime @default(now())

    @@index([buyerId])
    @@index([sellerId])
    @@index([completedAt])
}

// Social Features Models

model Syndicate {
    id          String   @id @default(cuid())
    name        String   @unique
    description String?  @db.Text
    leaderId    String   @unique
    leader      Player   @relation(fields: [leaderId], references: [id])
    
    level       Int      @default(1)
    experience  BigInt   @default(0)
    
    members     SyndicateMember[]
    createdAt   DateTime @default(now())

    @@index([name])
    @@index([level])
}

model SyndicateMember {
    id          String    @id @default(cuid())
    playerId    String    @unique
    player      Player    @relation(fields: [playerId], references: [id], onDelete: Cascade)
    syndicateId String
    syndicate   Syndicate @relation(fields: [syndicateId], references: [id], onDelete: Cascade)
    
    role        SyndicateRole @default(MEMBER)
    joinedAt    DateTime      @default(now())
    contribution BigInt       @default(0)

    @@index([syndicateId])
    @@index([role])
}

model DAOProposal {
    id          String   @id @default(cuid())
    title       String
    description String   @db.Text
    proposerId  String
    proposer    Player   @relation(fields: [proposerId], references: [id])
    
    votesFor    Int      @default(0)
    votesAgainst Int     @default(0)
    
    status      ProposalStatus @default(ACTIVE)
    createdAt   DateTime @default(now())
    expiresAt   DateTime
    
    votes       DAOVote[]

    @@index([proposerId])
    @@index([status])
    @@index([expiresAt])
}

model DAOVote {
    id         String      @id @default(cuid())
    proposalId String
    proposal   DAOProposal @relation(fields: [proposalId], references: [id], onDelete: Cascade)
    voterId    String
    voter      Player      @relation(fields: [voterId], references: [id])
    
    vote       VoteChoice
    weight     Int         // Voting power based on holdings/shards
    castAt     DateTime    @default(now())
    
    @@unique([proposalId, voterId])
    @@index([proposalId])
    @@index([voterId])
}

// Enums

enum RigType {
    USB_ASIC
    GPU_FARM
    ASIC_MINER
    QUANTUM_PROCESSOR
    FUSION_REACTOR
    DYSON_SPHERE
}

enum ShardRarity {
    COMMON
    UNCOMMON
    RARE
    EPIC
    LEGENDARY
    MYTHIC
}

enum ShardCategory {
    MINING_BOOST
    EFFICIENCY
    MARKET_ADVANTAGE
    SPECIAL_ABILITY
    PRESTIGE_BONUS
}

enum CurrencyType {
    CRYPTO_COIN
    ETHEREUM_G
    BYTE_COIN
    SOLANA_X
    CARDANO_Z
    POLKADOT_Y
    AVALANCHE_A
    COSMOS_C
    TEZOS_T
    ALGORAND_ALGO
}

enum SyndicateRole {
    LEADER
    OFFICER
    MEMBER
}

enum ProposalStatus {
    ACTIVE
    PASSED
    REJECTED
    EXPIRED
}

enum VoteChoice {
    FOR
    AGAINST
    ABSTAIN
}

// Necessary for Next auth
model Account {
    id                       String  @id @default(cuid())
    userId                   String
    type                     String
    provider                 String
    providerAccountId        String
    refresh_token            String? // @db.Text
    access_token             String? // @db.Text
    expires_at               Int?
    token_type               String?
    scope                    String?
    id_token                 String? // @db.Text
    session_state            String?
    user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)
    refresh_token_expires_in Int?

    @@unique([provider, providerAccountId])
}

model Session {
    id           String   @id @default(cuid())
    sessionToken String   @unique
    userId       String
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
    id            String    @id @default(cuid())
    name          String?
    email         String?   @unique
    emailVerified DateTime?
    image         String?
    accounts      Account[]
    sessions      Session[]
    player        Player?
}

model VerificationToken {
    identifier String
    token      String   @unique
    expires    DateTime

    @@unique([identifier, token])
}
