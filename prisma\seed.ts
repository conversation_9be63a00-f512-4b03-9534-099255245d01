import { PrismaClient } from '@prisma/client';
import { RigType, ShardRarity, ShardCategory, CurrencyType } from '../src/types/game';

const prisma = new PrismaClient();

// Mining Rig configurations
const rigConfigs = [
  {
    type: RigType.USB_ASIC,
    name: 'USB ASIC Miner',
    baseCost: 10,
    baseOutput: 0.1,
    costMultiplier: 1.15,
  },
  {
    type: RigType.GPU_FARM,
    name: 'GPU Mining Farm',
    baseCost: 100,
    baseOutput: 1.0,
    costMultiplier: 1.15,
  },
  {
    type: RigType.ASIC_MINER,
    name: 'Professional ASIC Miner',
    baseCost: 1000,
    baseOutput: 8.0,
    costMultiplier: 1.15,
  },
  {
    type: RigType.QUANTUM_PROCESSOR,
    name: 'Quantum Processing Unit',
    baseCost: 10000,
    baseOutput: 47.0,
    costMultiplier: 1.15,
  },
  {
    type: RigType.FUSION_REACTOR,
    name: 'Fusion-Powered Mining Reactor',
    baseCost: 100000,
    baseOutput: 260.0,
    costMultiplier: 1.15,
  },
  {
    type: RigType.DYSON_SPHERE,
    name: 'Dyson Sphere Mining Array',
    baseCost: 1000000,
    baseOutput: 1400.0,
    costMultiplier: 1.15,
  },
];

// Blockchain Shards seed data
const shardSeeds = [
  // Common Shards
  {
    name: 'Basic Hash Accelerator',
    rarity: ShardRarity.COMMON,
    category: ShardCategory.MINING_BOOST,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'click_power',
        value: 1.1,
      },
    ],
    description: 'A simple shard that slightly increases your clicking power.',
  },
  {
    name: 'Energy Saver',
    rarity: ShardRarity.COMMON,
    category: ShardCategory.EFFICIENCY,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'rig_efficiency',
        value: 1.05,
      },
    ],
    description: 'Reduces energy consumption of mining rigs by 5%.',
  },
  {
    name: 'Market Insight',
    rarity: ShardRarity.COMMON,
    category: ShardCategory.MARKET_ADVANTAGE,
    effects: [
      {
        type: 'FLAT_BONUS',
        target: 'market_prediction_accuracy',
        value: 5,
      },
    ],
    description: 'Provides basic market trend information.',
  },

  // Uncommon Shards
  {
    name: 'Overclocking Module',
    rarity: ShardRarity.UNCOMMON,
    category: ShardCategory.MINING_BOOST,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'mining_output',
        value: 1.15,
      },
    ],
    description: 'Overclocks your mining rigs for 15% increased output.',
  },
  {
    name: 'Cooling System',
    rarity: ShardRarity.UNCOMMON,
    category: ShardCategory.EFFICIENCY,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'rig_durability',
        value: 1.2,
      },
    ],
    description: 'Advanced cooling extends mining rig lifespan.',
  },
  {
    name: 'Price Oracle',
    rarity: ShardRarity.UNCOMMON,
    category: ShardCategory.MARKET_ADVANTAGE,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'trading_profit',
        value: 1.1,
      },
    ],
    description: 'Provides better market timing for trades.',
  },

  // Rare Shards
  {
    name: 'Quantum Entanglement Core',
    rarity: ShardRarity.RARE,
    category: ShardCategory.MINING_BOOST,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'all_mining',
        value: 1.25,
      },
    ],
    description: 'Quantum effects boost all mining operations by 25%.',
  },
  {
    name: 'Neural Network Optimizer',
    rarity: ShardRarity.RARE,
    category: ShardCategory.EFFICIENCY,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'research_speed',
        value: 1.3,
      },
    ],
    description: 'AI-powered optimization accelerates research progress.',
  },
  {
    name: 'Market Manipulation Device',
    rarity: ShardRarity.RARE,
    category: ShardCategory.MARKET_ADVANTAGE,
    effects: [
      {
        type: 'SPECIAL_ABILITY',
        target: 'market_influence',
        value: 1,
      },
    ],
    description: 'Allows minor influence over cryptocurrency prices.',
  },

  // Epic Shards
  {
    name: 'Blockchain Genesis Block',
    rarity: ShardRarity.EPIC,
    category: ShardCategory.SPECIAL_ABILITY,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'all_production',
        value: 1.5,
      },
      {
        type: 'FLAT_BONUS',
        target: 'prestige_points',
        value: 10,
      },
    ],
    description: 'A fragment of the original blockchain, providing massive bonuses.',
  },
  {
    name: 'Time Dilation Field',
    rarity: ShardRarity.EPIC,
    category: ShardCategory.SPECIAL_ABILITY,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'offline_production',
        value: 2.0,
      },
    ],
    description: 'Doubles offline production through temporal manipulation.',
  },

  // Legendary Shards
  {
    name: 'Satoshi\'s Lost Wallet',
    rarity: ShardRarity.LEGENDARY,
    category: ShardCategory.PRESTIGE_BONUS,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'all_currencies',
        value: 2.0,
      },
      {
        type: 'SPECIAL_ABILITY',
        target: 'legendary_power',
        value: 1,
      },
    ],
    description: 'The legendary lost wallet of Bitcoin\'s creator. Doubles all currency generation.',
  },
  {
    name: 'Multiverse Mining Array',
    rarity: ShardRarity.LEGENDARY,
    category: ShardCategory.MINING_BOOST,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'dimensional_mining',
        value: 3.0,
      },
    ],
    description: 'Mines cryptocurrency across multiple dimensions simultaneously.',
  },

  // Mythic Shards
  {
    name: 'The Singularity Core',
    rarity: ShardRarity.MYTHIC,
    category: ShardCategory.SPECIAL_ABILITY,
    effects: [
      {
        type: 'MULTIPLIER',
        target: 'everything',
        value: 10.0,
      },
      {
        type: 'SPECIAL_ABILITY',
        target: 'reality_manipulation',
        value: 1,
      },
    ],
    description: 'The ultimate shard. Transcends normal game mechanics and multiplies everything by 10x.',
  },
];

// Initial cryptocurrency prices
const initialPrices = [
  {
    currency: CurrencyType.CRYPTO_COIN,
    price: 1.0,
    volume24h: 1000000,
    volatility: 0.05,
  },
  {
    currency: CurrencyType.ETHEREUM_G,
    price: 2000.0,
    volume24h: 500000,
    volatility: 0.08,
  },
  {
    currency: CurrencyType.BYTE_COIN,
    price: 0.5,
    volume24h: 2000000,
    volatility: 0.12,
  },
  {
    currency: CurrencyType.SOLANA_X,
    price: 100.0,
    volume24h: 800000,
    volatility: 0.15,
  },
  {
    currency: CurrencyType.CARDANO_Z,
    price: 0.8,
    volume24h: 1500000,
    volatility: 0.10,
  },
  {
    currency: CurrencyType.POLKADOT_Y,
    price: 25.0,
    volume24h: 600000,
    volatility: 0.13,
  },
];

async function main() {
  console.log('🌱 Starting database seed...');

  // Seed Blockchain Shards
  console.log('📦 Seeding Blockchain Shards...');
  for (const shard of shardSeeds) {
    await prisma.blockchainShard.upsert({
      where: { name: shard.name },
      update: {},
      create: {
        name: shard.name,
        rarity: shard.rarity,
        category: shard.category,
        effects: shard.effects,
        description: shard.description,
      },
    });
  }

  // Seed initial cryptocurrency prices
  console.log('💰 Seeding cryptocurrency prices...');
  for (const price of initialPrices) {
    await prisma.cryptocurrencyPrice.create({
      data: {
        currency: price.currency,
        price: price.price,
        volume24h: price.volume24h,
        volatility: price.volatility,
      },
    });
  }

  console.log('✅ Database seeded successfully!');
  console.log(`📊 Created ${shardSeeds.length} Blockchain Shards`);
  console.log(`💱 Created ${initialPrices.length} cryptocurrency price entries`);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Seed failed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });