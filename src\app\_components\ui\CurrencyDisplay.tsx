"use client";

import { CurrencyType } from "~/types/game";

interface CurrencyDisplayProps {
  amount: number;
  currency: CurrencyType;
  showIcon?: boolean;
  className?: string;
}

const currencyConfig = {
  [CurrencyType.CRYPTO_COIN]: {
    symbol: "CTC",
    icon: "₿",
    color: "text-orange-400",
  },
  [CurrencyType.ETHEREUM_G]: {
    symbol: "ETH-G",
    icon: "Ξ",
    color: "text-blue-400",
  },
  [CurrencyType.BYTE_COIN]: {
    symbol: "BTC",
    icon: "₿",
    color: "text-yellow-400",
  },
  [CurrencyType.SOLANA_X]: {
    symbol: "SOL-X",
    icon: "◎",
    color: "text-purple-400",
  },
  [CurrencyType.CARDANO_Z]: {
    symbol: "ADA-Z",
    icon: "₳",
    color: "text-blue-300",
  },
  [CurrencyType.POLKADOT_Y]: {
    symbol: "DOT-Y",
    icon: "●",
    color: "text-pink-400",
  },
};

export function CurrencyDisplay({ 
  amount, 
  currency, 
  showIcon = false, 
  className = "" 
}: CurrencyDisplayProps) {
  const config = currencyConfig[currency];
  
  const formatAmount = (value: number): string => {
    if (value >= 1e12) return `${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `${(value / 1e3).toFixed(2)}K`;
    return value.toFixed(2);
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {showIcon && (
        <span className={`font-bold ${config.color}`}>
          {config.icon}
        </span>
      )}
      <span className={`font-medium ${config.color}`}>
        {formatAmount(amount)}
      </span>
      <span className="text-gray-400 text-sm">
        {config.symbol}
      </span>
    </div>
  );
}