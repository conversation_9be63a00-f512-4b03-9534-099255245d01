import { TRPCError } from "@trpc/server";
import { z } from "zod";
import {
  ClickMineInputSchema,
  PurchaseRigInputSchema,
  UpgradeRigInputSchema,
  SwitchMiningTargetInputSchema,
  ClickMineResponseSchema,
  PurchaseRigResponseSchema,
  OfflineProductionResponseSchema
} from "~/schemas/game";
import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";
import {
  calculateTotalCPS,
  calculateRigCost,
  calculateUpgradeCost,
  calculateOfflineProduction,
  calculateClickPower,
  applyShardBonuses,
  decimalToNumber,
  numberToDecimal,
  validateClickRate,
  validateClickPattern,
  isRigUnlocked,
  getAvailableRigTypes,
  calculateRigOutput,
  getRigTierInfo,
  getNextUpgradeCost,
  getNextPurchaseCost
} from "~/lib/game-utils";
import { RigType, CurrencyType } from "~/types/game";
import { RIG_CONFIGS, GAME_CONSTANTS } from "~/config/game-constants";

// Rate limiting storage (in production, use Redis)
const clickRateTracker = new Map<string, number[]>();

// Enhanced rate limiting with burst protection
const burstTracker = new Map<string, { count: number; resetTime: number }>();

function checkBurstLimit(userId: string): boolean {
  const now = Date.now();
  const burst = burstTracker.get(userId);

  if (!burst || now > burst.resetTime) {
    burstTracker.set(userId, { count: 1, resetTime: now + 1000 }); // 1 second window
    return true;
  }

  if (burst.count >= GAME_CONSTANTS.CLICK_BURST_LIMIT) {
    return false;
  }

  burst.count++;
  return true;
}

// Export rate limiting trackers for background job cleanup
export function getClickRateTracker() {
  return clickRateTracker;
}

export function getBurstTracker() {
  return burstTracker;
}

export const gameRouter = createTRPCRouter({
  // Get player's current game state
  getPlayerState: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session.user.id;

    // Get or create player
    let player = await ctx.db.player.findUnique({
      where: { userId },
      include: {
        miningRigs: true,
        shards: {
          include: {
            shard: true
          }
        },
        research: true,
        dataCenters: true,
      },
    });

    if (!player) {
      // Create new player with starting values
      player = await ctx.db.player.create({
        data: {
          userId,
          cryptoCoin: 10, // Starting CTC
          ethereumG: 0,
          byteCoin: 0,
          solanaX: 0,
          totalClicks: 0,
          prestigeLevel: 0,
          blockchainPoints: 0,
        },
        include: {
          miningRigs: true,
          shards: {
            include: {
              shard: true
            }
          },
          research: true,
          dataCenters: true,
        },
      });
    }

    // Calculate current CPS
    const totalCPS = calculateTotalCPS(player.miningRigs as any);
    const bonusedCPS = applyShardBonuses(totalCPS, player.shards as any, 'mining_output');

    return {
      player: {
        id: player.id,
        userId: player.userId,
        currencies: {
          cryptoCoin: player.cryptoCoin,
          ethereumG: player.ethereumG,
          byteCoin: player.byteCoin,
          solanaX: player.solanaX,
        },
        stats: {
          totalClicks: player.totalClicks,
          prestigeLevel: player.prestigeLevel,
          blockchainPoints: player.blockchainPoints,
          cpsRate: bonusedCPS,
        },
        timestamps: {
          lastActiveAt: player.lastActiveAt,
          createdAt: player.createdAt,
          updatedAt: player.updatedAt,
        },
      },
      miningRigs: player.miningRigs,
      shards: player.shards,
      research: player.research,
      dataCenters: player.dataCenters,
    };
  }),

  // DEPRECATED: Click mining now handled via WebSocket for real-time performance
  // This endpoint is kept for backward compatibility but should not be used
  clickMine: protectedProcedure
    .input(ClickMineInputSchema)
    .mutation(async ({ ctx, input }) => {
      throw new TRPCError({
        code: "METHOD_NOT_SUPPORTED",
        message: "Click mining is now handled via WebSocket. Please use the real-time WebSocket connection for clicking.",
      });
    }),

  // Purchase mining rigs
  purchaseRig: protectedProcedure
    .input(PurchaseRigInputSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { rigType, quantity } = input;

      const player = await ctx.db.player.findUnique({
        where: { userId },
        include: {
          miningRigs: {
            where: { rigType }
          },
          research: true,
          dataCenters: true,
        }
      });

      if (!player) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Player not found",
        });
      }

      // Check if rig type is valid
      const rigConfig = RIG_CONFIGS[rigType];
      if (!rigConfig) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid rig type",
        });
      }

      // Check if rig type is unlocked
      const playerState = {
        currencies: {
          cryptoCoin: player.cryptoCoin,
          ethereumG: player.ethereumG,
          byteCoin: player.byteCoin,
          solanaX: player.solanaX,
        },
        totalClicks: player.totalClicks,
        prestigeLevel: player.prestigeLevel,
      };

      if (!isRigUnlocked(rigType, playerState, player.research, player.dataCenters)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Rig type not yet unlocked",
        });
      }

      // Validate quantity limits
      if (quantity > 100) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot purchase more than 100 rigs at once",
        });
      }

      // Calculate current quantity of this rig type
      const currentQuantity = player.miningRigs.reduce((sum, rig) => sum + rig.quantity, 0);

      // Check if player would exceed maximum rigs per type
      const maxRigsPerType = 10000;
      if (currentQuantity + quantity > maxRigsPerType) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Cannot own more than ${maxRigsPerType} rigs of the same type`,
        });
      }

      // Calculate total cost
      const totalCost = calculateRigCost(rigType, currentQuantity, quantity);
      const currentBalance = decimalToNumber(player.cryptoCoin);

      if (currentBalance < totalCost) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Insufficient funds. Need ${totalCost.toFixed(2)} CTC, have ${currentBalance.toFixed(2)} CTC`,
        });
      }

      // Validate cost calculation
      if (totalCost <= 0 || !isFinite(totalCost)) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Invalid cost calculation",
        });
      }

      // Create or update mining rig
      let rigId: string;
      const existingRig = player.miningRigs[0];

      if (existingRig) {
        // Update existing rig
        const updatedRig = await ctx.db.miningRig.update({
          where: { id: existingRig.id },
          data: {
            quantity: {
              increment: quantity
            }
          }
        });
        rigId = updatedRig.id;
      } else {
        // Create new rig
        const newRig = await ctx.db.miningRig.create({
          data: {
            playerId: player.id,
            rigType,
            level: 1,
            quantity,
            baseOutput: rigConfig.baseOutput,
            efficiency: 1.0,
          }
        });
        rigId = newRig.id;
      }

      // Deduct cost from player balance
      const updatedPlayer = await ctx.db.player.update({
        where: { userId },
        data: {
          cryptoCoin: {
            decrement: totalCost
          },
          lastActiveAt: new Date(),
        }
      });

      const newBalance = decimalToNumber(updatedPlayer.cryptoCoin);

      return PurchaseRigResponseSchema.parse({
        success: true,
        rigId,
        totalCost,
        newBalance,
      });
    }),

  // Upgrade mining rig
  upgradeRig: protectedProcedure
    .input(UpgradeRigInputSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { rigId, levels } = input;

      const player = await ctx.db.player.findUnique({
        where: { userId },
      });

      if (!player) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Player not found",
        });
      }

      const rig = await ctx.db.miningRig.findUnique({
        where: { id: rigId },
      });

      if (!rig || rig.playerId !== player.id) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Mining rig not found",
        });
      }

      // Validate upgrade levels
      if (levels > 10) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot upgrade more than 10 levels at once",
        });
      }

      // Check maximum level limit based on rig configuration
      const rigConfig = RIG_CONFIGS[rig.rigType];
      const maxLevel = rigConfig?.maxLevel || 100;
      if (rig.level + levels > maxLevel) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Cannot exceed maximum level of ${maxLevel} for ${rigConfig?.name || 'this rig'}`,
        });
      }

      // Calculate upgrade cost
      const upgradeCost = calculateUpgradeCost(rig.rigType, rig.level, levels);
      const currentBalance = decimalToNumber(player.cryptoCoin);

      if (currentBalance < upgradeCost) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Insufficient funds for upgrade. Need ${upgradeCost.toFixed(2)} CTC, have ${currentBalance.toFixed(2)} CTC`,
        });
      }

      // Validate cost calculation
      if (upgradeCost <= 0 || !isFinite(upgradeCost)) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Invalid upgrade cost calculation",
        });
      }

      // Update rig level and player balance
      await ctx.db.$transaction([
        ctx.db.miningRig.update({
          where: { id: rigId },
          data: {
            level: {
              increment: levels
            }
          }
        }),
        ctx.db.player.update({
          where: { userId },
          data: {
            cryptoCoin: {
              decrement: upgradeCost
            },
            lastActiveAt: new Date(),
          }
        })
      ]);

      const updatedPlayer = await ctx.db.player.findUnique({
        where: { userId }
      });

      const newBalance = decimalToNumber(updatedPlayer!.cryptoCoin);

      return {
        success: true,
        upgradeCost,
        newBalance,
        newLevel: rig.level + levels,
      };
    }),

  // Switch mining target currency (placeholder for future multi-currency support)
  switchMiningTarget: protectedProcedure
    .input(SwitchMiningTargetInputSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { currency } = input;

      // For now, only CryptoCoin is supported
      if (currency !== CurrencyType.CRYPTO_COIN) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Currency not yet unlocked",
        });
      }

      await ctx.db.player.update({
        where: { userId },
        data: {
          lastActiveAt: new Date(),
        }
      });

      return {
        success: true,
        newTarget: currency,
      };
    }),

  // Calculate and award offline production
  calculateOfflineProduction: protectedProcedure
    .mutation(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      const player = await ctx.db.player.findUnique({
        where: { userId },
        include: {
          miningRigs: true,
          shards: {
            where: { equipped: true },
            include: { shard: true }
          }
        }
      });

      if (!player) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Player not found",
        });
      }

      // Calculate offline time
      const now = new Date();
      const lastActive = player.lastActiveAt;
      const offlineTimeSeconds = Math.floor((now.getTime() - lastActive.getTime()) / 1000);

      // Only calculate if player was offline for more than 1 minute
      if (offlineTimeSeconds < 60) {
        return OfflineProductionResponseSchema.parse({
          success: true,
          offlineTime: offlineTimeSeconds,
          totalProduction: 0,
          newBalance: decimalToNumber(player.cryptoCoin),
        });
      }

      // Calculate offline production
      const totalProduction = calculateOfflineProduction(
        player.miningRigs as any,
        player.shards as any,
        offlineTimeSeconds
      );

      // Update player balance
      const updatedPlayer = await ctx.db.player.update({
        where: { userId },
        data: {
          cryptoCoin: {
            increment: totalProduction
          },
          lastActiveAt: now,
        }
      });

      const newBalance = decimalToNumber(updatedPlayer.cryptoCoin);

      return OfflineProductionResponseSchema.parse({
        success: true,
        offlineTime: offlineTimeSeconds,
        totalProduction,
        newBalance,
      });
    }),

  // Get available mining rigs for purchase
  getAvailableRigs: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session.user.id;

    const player = await ctx.db.player.findUnique({
      where: { userId },
      include: {
        miningRigs: true,
        research: true,
        dataCenters: true,
      }
    });

    if (!player) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Player not found",
      });
    }

    // Create player state for unlock checking
    const playerState = {
      currencies: {
        cryptoCoin: player.cryptoCoin,
        ethereumG: player.ethereumG,
        byteCoin: player.byteCoin,
        solanaX: player.solanaX,
      },
      totalClicks: player.totalClicks,
      prestigeLevel: player.prestigeLevel,
    };

    // Return all rig types with unlock status and tier information
    const availableRigs = Object.entries(RIG_CONFIGS).map(([rigType, config]) => {
      const existingRig = player.miningRigs.find(rig => rig.rigType === rigType);
      const currentQuantity = existingRig?.quantity || 0;
      const currentLevel = existingRig?.level || 1;
      const isUnlocked = isRigUnlocked(rigType, playerState, player.research, player.dataCenters);
      const nextPurchaseCost = getNextPurchaseCost(rigType, currentQuantity);
      const nextUpgradeCost = existingRig ? getNextUpgradeCost(rigType, currentLevel) : null;
      const tierInfo = getRigTierInfo(rigType);

      return {
        type: rigType as RigType,
        name: config.name,
        description: config.description,
        baseCost: config.baseCost,
        baseOutput: config.baseOutput,
        costMultiplier: config.costMultiplier,
        tier: tierInfo?.tier || 1,
        maxLevel: tierInfo?.maxLevel || 100,
        unlockRequirement: tierInfo?.unlockRequirement,
        isUnlocked,
        currentQuantity,
        currentLevel,
        nextPurchaseCost,
        nextUpgradeCost,
        totalOutput: existingRig ? calculateRigOutput(existingRig as any) : 0,
      };
    });

    // Sort by tier for better UI organization
    availableRigs.sort((a, b) => a.tier - b.tier);

    return availableRigs;
  }),
});