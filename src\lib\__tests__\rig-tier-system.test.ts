import { describe, it, expect } from 'vitest';
import {
  isRigUnlocked,
  getAvailableRigTypes,
  calculateRigOutput,
  getRigTierInfo,
  getNextUpgradeCost,
  getNextPurchaseCost,
} from '../game-utils';
import { RigType, CurrencyType } from '../../types/game';
import { RIG_CONFIGS } from '../../config/game-constants';

describe('Rig Tier System', () => {
  const mockPlayerState = {
    currencies: {
      cryptoCoin: 1000,
      ethereumG: 0,
      byteCoin: 0,
      solanaX: 0,
    },
    totalClicks: 100,
    prestigeLevel: 0,
  };

  const mockPlayerStateRich = {
    currencies: {
      cryptoCoin: 1000000,
      ethereumG: 0,
      byteCoin: 0,
      solanaX: 0,
    },
    totalClicks: 10000,
    prestigeLevel: 1,
  };

  describe('Rig Unlock System', () => {
    it('should unlock USB_ASIC by default', () => {
      const isUnlocked = isRigUnlocked(RigType.USB_ASIC, mockPlayerState);
      expect(isUnlocked).toBe(true);
    });

    it('should unlock GPU_FARM when player has enough CTC', () => {
      const isUnlocked = isRigUnlocked(RigType.GPU_FARM, mockPlayerState);
      expect(isUnlocked).toBe(true); // Player has 1000 CTC, needs 500
    });

    it('should not unlock ASIC_MINER when player lacks CTC', () => {
      const isUnlocked = isRigUnlocked(RigType.ASIC_MINER, mockPlayerState);
      expect(isUnlocked).toBe(false); // Player has 1000 CTC, needs 5000
    });

    it('should unlock higher tier rigs for rich player', () => {
      const isUnlocked = isRigUnlocked(RigType.QUANTUM_PROCESSOR, mockPlayerStateRich);
      expect(isUnlocked).toBe(true); // Rich player has 1M CTC, needs 50K
    });

    it('should return available rig types based on unlock conditions', () => {
      const availableRigs = getAvailableRigTypes(mockPlayerState);
      expect(availableRigs).toContain(RigType.USB_ASIC);
      expect(availableRigs).toContain(RigType.GPU_FARM);
      expect(availableRigs).not.toContain(RigType.ASIC_MINER);
    });

    it('should return more rig types for rich player', () => {
      const availableRigs = getAvailableRigTypes(mockPlayerStateRich);
      expect(availableRigs).toContain(RigType.USB_ASIC);
      expect(availableRigs).toContain(RigType.GPU_FARM);
      expect(availableRigs).toContain(RigType.ASIC_MINER);
      expect(availableRigs).toContain(RigType.QUANTUM_PROCESSOR);
    });
  });

  describe('Rig Output Calculation', () => {
    it('should calculate rig output correctly', () => {
      const mockRig = {
        id: 'test-rig',
        playerId: 'test-player',
        rigType: RigType.GPU_FARM,
        level: 5,
        quantity: 3,
        baseOutput: 1.0,
        efficiency: 1.2,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const output = calculateRigOutput(mockRig);
      // baseOutput (1.0) * level (5) * quantity (3) * efficiency (1.2) = 18.0
      expect(output).toBe(18.0);
    });

    it('should handle Decimal baseOutput and efficiency', () => {
      const mockRig = {
        id: 'test-rig',
        playerId: 'test-player',
        rigType: RigType.USB_ASIC,
        level: 2,
        quantity: 1,
        baseOutput: { toNumber: () => 0.1 } as any,
        efficiency: { toNumber: () => 1.5 } as any,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const output = calculateRigOutput(mockRig);
      // 0.1 * 2 * 1 * 1.5 = 0.3
      expect(output).toBeCloseTo(0.3, 10);
    });
  });

  describe('Rig Tier Information', () => {
    it('should return correct tier info for USB_ASIC', () => {
      const tierInfo = getRigTierInfo(RigType.USB_ASIC);
      expect(tierInfo).toEqual({
        tier: 1,
        maxLevel: 100,
        unlockRequirement: null,
      });
    });

    it('should return correct tier info for GPU_FARM', () => {
      const tierInfo = getRigTierInfo(RigType.GPU_FARM);
      expect(tierInfo).toEqual({
        tier: 2,
        maxLevel: 100,
        unlockRequirement: {
          type: 'CURRENCY',
          currency: CurrencyType.CRYPTO_COIN,
          value: 500,
        },
      });
    });

    it('should return null for invalid rig type', () => {
      const tierInfo = getRigTierInfo('INVALID_RIG' as RigType);
      expect(tierInfo).toBeNull();
    });
  });

  describe('Cost Calculations', () => {
    it('should calculate next upgrade cost correctly', () => {
      const cost = getNextUpgradeCost(RigType.USB_ASIC, 1);
      const config = RIG_CONFIGS[RigType.USB_ASIC];
      const expectedCost = config.baseCost * Math.pow(config.upgradeCostMultiplier, 1);
      expect(cost).toBe(expectedCost);
    });

    it('should calculate next purchase cost correctly', () => {
      const cost = getNextPurchaseCost(RigType.USB_ASIC, 0);
      const config = RIG_CONFIGS[RigType.USB_ASIC];
      expect(cost).toBe(config.baseCost);
    });

    it('should calculate escalating purchase costs', () => {
      const cost1 = getNextPurchaseCost(RigType.USB_ASIC, 0);
      const cost2 = getNextPurchaseCost(RigType.USB_ASIC, 1);
      const cost3 = getNextPurchaseCost(RigType.USB_ASIC, 2);
      
      expect(cost2).toBeGreaterThan(cost1);
      expect(cost3).toBeGreaterThan(cost2);
    });

    it('should calculate escalating upgrade costs', () => {
      const cost1 = getNextUpgradeCost(RigType.USB_ASIC, 1);
      const cost2 = getNextUpgradeCost(RigType.USB_ASIC, 2);
      const cost3 = getNextUpgradeCost(RigType.USB_ASIC, 3);
      
      expect(cost2).toBeGreaterThan(cost1);
      expect(cost3).toBeGreaterThan(cost2);
    });
  });

  describe('Rig Configuration Validation', () => {
    it('should have valid configuration for all rig types', () => {
      Object.entries(RIG_CONFIGS).forEach(([rigType, config]) => {
        expect(config.name).toBeTruthy();
        expect(config.baseCost).toBeGreaterThan(0);
        expect(config.baseOutput).toBeGreaterThan(0);
        expect(config.costMultiplier).toBeGreaterThan(1);
        expect(config.upgradeCostMultiplier).toBeGreaterThan(1);
        expect(config.tier).toBeGreaterThan(0);
        expect(config.maxLevel).toBeGreaterThan(0);
        expect(config.description).toBeTruthy();
      });
    });

    it('should have ascending tier numbers', () => {
      const tiers = Object.values(RIG_CONFIGS).map(config => config.tier);
      const sortedTiers = [...tiers].sort((a, b) => a - b);
      expect(tiers).toEqual(sortedTiers);
    });

    it('should have ascending base costs by tier', () => {
      const rigsByTier = Object.values(RIG_CONFIGS).sort((a, b) => a.tier - b.tier);
      for (let i = 1; i < rigsByTier.length; i++) {
        expect(rigsByTier[i].baseCost).toBeGreaterThan(rigsByTier[i - 1].baseCost);
      }
    });

    it('should have ascending base output by tier', () => {
      const rigsByTier = Object.values(RIG_CONFIGS).sort((a, b) => a.tier - b.tier);
      for (let i = 1; i < rigsByTier.length; i++) {
        expect(rigsByTier[i].baseOutput).toBeGreaterThan(rigsByTier[i - 1].baseOutput);
      }
    });
  });
});