import { RigType, CurrencyType, ShardRarity } from "../types/game";

// Mining Rig Configurations with Tier System
export const RIG_CONFIGS = {
  [RigType.USB_ASIC]: {
    name: 'USB ASIC Miner',
    baseCost: 10,
    baseOutput: 0.1,
    costMultiplier: 1.15,
    upgradeCostMultiplier: 1.5,
    tier: 1,
    maxLevel: 100,
    description: 'A basic USB-powered ASIC miner. Your first step into automated mining.',
    unlockRequirement: null, // Always available
  },
  [RigType.GPU_FARM]: {
    name: 'GPU Mining Farm',
    baseCost: 100,
    baseOutput: 1.0,
    costMultiplier: 1.15,
    upgradeCostMultiplier: 1.5,
    tier: 2,
    maxLevel: 100,
    description: 'A collection of graphics cards working together to mine cryptocurrency.',
    unlockRequirement: {
      type: 'CURRENCY',
      currency: CurrencyType.CRYPTO_COIN,
      value: 500,
    },
  },
  [RigType.ASIC_MINER]: {
    name: 'Professional ASIC Miner',
    baseCost: 1000,
    baseOutput: 8.0,
    costMultiplier: 1.15,
    upgradeCostMultiplier: 1.5,
    tier: 3,
    maxLevel: 100,
    description: 'Industrial-grade ASIC hardware for serious mining operations.',
    unlockRequirement: {
      type: 'CURRENCY',
      currency: CurrencyType.CRYPTO_COIN,
      value: 5000,
    },
  },
  [RigType.QUANTUM_PROCESSOR]: {
    name: 'Quantum Processing Unit',
    baseCost: 10000,
    baseOutput: 47.0,
    costMultiplier: 1.15,
    upgradeCostMultiplier: 1.5,
    tier: 4,
    maxLevel: 50,
    description: 'Quantum computing power applied to cryptocurrency mining.',
    unlockRequirement: {
      type: 'CURRENCY',
      currency: CurrencyType.CRYPTO_COIN,
      value: 50000,
    },
  },
  [RigType.FUSION_REACTOR]: {
    name: 'Fusion-Powered Mining Reactor',
    baseCost: 100000,
    baseOutput: 260.0,
    costMultiplier: 1.15,
    upgradeCostMultiplier: 1.5,
    tier: 5,
    maxLevel: 25,
    description: 'Nuclear fusion provides unlimited power for massive mining operations.',
    unlockRequirement: {
      type: 'CURRENCY',
      currency: CurrencyType.CRYPTO_COIN,
      value: 500000,
    },
  },
  [RigType.DYSON_SPHERE]: {
    name: 'Dyson Sphere Mining Array',
    baseCost: 1000000,
    baseOutput: 1400.0,
    costMultiplier: 1.15,
    upgradeCostMultiplier: 1.5,
    tier: 6,
    maxLevel: 10,
    description: 'Harness the power of an entire star for ultimate mining capacity.',
    unlockRequirement: {
      type: 'CURRENCY',
      currency: CurrencyType.CRYPTO_COIN,
      value: 5000000,
    },
  },
} as const;

// Currency Configurations
export const CURRENCY_CONFIGS = {
  [CurrencyType.CRYPTO_COIN]: {
    name: 'CryptoCoin',
    symbol: 'CTC',
    color: '#f7931a',
    description: 'The original cryptocurrency of the game.',
    baseValue: 1.0,
  },
  [CurrencyType.ETHEREUM_G]: {
    name: 'EthereumG',
    symbol: 'ETHG',
    color: '#627eea',
    description: 'A smart contract platform currency.',
    baseValue: 2000.0,
  },
  [CurrencyType.BYTE_COIN]: {
    name: 'ByteCoin',
    symbol: 'BTC',
    color: '#ff6b35',
    description: 'A privacy-focused cryptocurrency.',
    baseValue: 0.5,
  },
  [CurrencyType.SOLANA_X]: {
    name: 'SolanaX',
    symbol: 'SOLX',
    color: '#9945ff',
    description: 'High-performance blockchain currency.',
    baseValue: 100.0,
  },
  [CurrencyType.CARDANO_Z]: {
    name: 'CardanoZ',
    symbol: 'ADAZ',
    color: '#0033ad',
    description: 'Research-driven blockchain currency.',
    baseValue: 0.8,
  },
  [CurrencyType.POLKADOT_Y]: {
    name: 'PolkadotY',
    symbol: 'DOTY',
    color: '#e6007a',
    description: 'Multi-chain interoperability currency.',
    baseValue: 25.0,
  },
} as const;

// Game Balance Constants
export const GAME_CONSTANTS = {
  // Click Mining
  BASE_CLICK_POWER: 1,
  CLICK_RATE_LIMIT: 20, // clicks per second
  CLICK_RATE_WINDOW: 1000, // milliseconds

  // Offline Production
  MAX_OFFLINE_HOURS: 24,
  OFFLINE_EFFICIENCY_THRESHOLD: 8, // hours
  OFFLINE_EFFICIENCY_PENALTY: 0.8, // 80% efficiency after threshold

  // Prestige System
  PRESTIGE_BASE_REQUIREMENT: 1000000, // CTC needed for first prestige
  PRESTIGE_MULTIPLIER: 2.5,
  BLOCKCHAIN_POINTS_BASE: 1,

  // Shard Drop Rates (per 1000 clicks or actions)
  SHARD_DROP_RATES: {
    [ShardRarity.COMMON]: 50, // 5%
    [ShardRarity.UNCOMMON]: 20, // 2%
    [ShardRarity.RARE]: 8, // 0.8%
    [ShardRarity.EPIC]: 3, // 0.3%
    [ShardRarity.LEGENDARY]: 1, // 0.1%
    [ShardRarity.MYTHIC]: 0.1, // 0.01%
  },

  // Market Simulation
  MARKET_UPDATE_INTERVAL: 5000, // milliseconds
  BASE_VOLATILITY: 0.05,
  MAX_PRICE_CHANGE: 0.2, // 20% max change per update

  // Research System
  RESEARCH_BASE_COST: 1000,
  RESEARCH_COST_MULTIPLIER: 1.5,

  // Data Center Locations
  LOCATION_BASE_COST: 10000,
  LOCATION_COST_MULTIPLIER: 2.0,

  // Trading System
  TRADE_FEE_PERCENTAGE: 0.02, // 2% fee
  MAX_TRADE_LISTINGS_PER_PLAYER: 10,
  TRADE_LISTING_DURATION: 604800, // 1 week in seconds

  // Social Features
  SYNDICATE_MAX_MEMBERS: 50,
  SYNDICATE_CREATION_COST: 100000,
  DAO_PROPOSAL_COST: 50000,
  DAO_VOTING_DURATION: 604800, // 1 week

  // Mini-games
  HACKATHON_COOLDOWN: 3600, // 1 hour
  HACKATHON_REWARD_MULTIPLIER: 2.0,

  // Rate Limiting
  API_RATE_LIMIT: 100, // requests per minute
  CLICK_BURST_LIMIT: 50, // max clicks in burst
} as const;

// Research Tree Configuration
export const RESEARCH_TREE = {
  'basic_efficiency': {
    name: 'Basic Efficiency',
    description: 'Improves mining rig efficiency by 10%',
    cost: 1000,
    currency: CurrencyType.CRYPTO_COIN,
    prerequisites: [],
    unlocks: ['advanced_efficiency', 'ethereum_mining'],
    effects: [
      { type: 'MULTIPLIER', target: 'rig_efficiency', value: 1.1 }
    ],
  },
  'ethereum_mining': {
    name: 'Ethereum Mining',
    description: 'Unlocks the ability to mine EthereumG',
    cost: 5000,
    currency: CurrencyType.CRYPTO_COIN,
    prerequisites: ['basic_efficiency'],
    unlocks: ['smart_contracts', 'bytecoin_mining'],
    effects: [
      { type: 'UNLOCK', target: 'currency', value: CurrencyType.ETHEREUM_G }
    ],
  },
  'bytecoin_mining': {
    name: 'ByteCoin Mining',
    description: 'Unlocks the ability to mine ByteCoin',
    cost: 10000,
    currency: CurrencyType.ETHEREUM_G,
    prerequisites: ['ethereum_mining'],
    unlocks: ['privacy_protocols', 'solana_mining'],
    effects: [
      { type: 'UNLOCK', target: 'currency', value: CurrencyType.BYTE_COIN }
    ],
  },
  'advanced_efficiency': {
    name: 'Advanced Efficiency',
    description: 'Further improves mining rig efficiency by 25%',
    cost: 25000,
    currency: CurrencyType.CRYPTO_COIN,
    prerequisites: ['basic_efficiency'],
    unlocks: ['quantum_computing'],
    effects: [
      { type: 'MULTIPLIER', target: 'rig_efficiency', value: 1.25 }
    ],
  },
  'quantum_computing': {
    name: 'Quantum Computing',
    description: 'Unlocks Quantum Processor mining rigs',
    cost: 100000,
    currency: CurrencyType.CRYPTO_COIN,
    prerequisites: ['advanced_efficiency'],
    unlocks: ['fusion_power'],
    effects: [
      { type: 'UNLOCK', target: 'rig_type', value: RigType.QUANTUM_PROCESSOR }
    ],
  },
} as const;

// Data Center Locations
export const DATA_CENTER_LOCATIONS = {
  'silicon_valley': {
    name: 'Silicon Valley',
    description: 'The heart of tech innovation',
    unlockCost: 10000,
    currency: CurrencyType.CRYPTO_COIN,
    bonuses: [
      { type: 'research_speed', value: 1.2 },
      { type: 'rig_efficiency', value: 1.1 },
    ],
  },
  'iceland': {
    name: 'Iceland Geothermal Facility',
    description: 'Cheap renewable energy for mining',
    unlockCost: 50000,
    currency: CurrencyType.CRYPTO_COIN,
    bonuses: [
      { type: 'energy_cost_reduction', value: 0.5 },
      { type: 'cooling_efficiency', value: 1.3 },
    ],
  },
  'moon_base': {
    name: 'Lunar Mining Station',
    description: 'Zero gravity, maximum efficiency',
    unlockCost: 1000000,
    currency: CurrencyType.ETHEREUM_G,
    bonuses: [
      { type: 'all_mining', value: 2.0 },
      { type: 'shard_drop_rate', value: 1.5 },
    ],
    requirements: [
      { type: 'research', value: 'space_technology' },
    ],
  },
  'mars_colony': {
    name: 'Mars Mining Colony',
    description: 'The ultimate frontier for mining',
    unlockCost: 10000000,
    currency: CurrencyType.SOLANA_X,
    bonuses: [
      { type: 'all_production', value: 5.0 },
      { type: 'prestige_bonus', value: 2.0 },
    ],
    requirements: [
      { type: 'research', value: 'interplanetary_mining' },
      { type: 'location', value: 'moon_base' },
    ],
  },
} as const;

// Event System Configuration
export const BLOCKCHAIN_EVENTS = {
  'bull_run': {
    name: 'Crypto Bull Run',
    type: 'POSITIVE' as const,
    description: 'Market sentiment is extremely positive!',
    probability: 0.05,
    duration: 300, // 5 minutes
    effects: [
      { target: 'all_currency_value', modifier: 1.5, duration: 300 },
      { target: 'trading_volume', modifier: 2.0, duration: 300 },
    ],
  },
  'market_crash': {
    name: 'Market Crash',
    type: 'NEGATIVE' as const,
    description: 'Cryptocurrency markets are crashing!',
    probability: 0.03,
    duration: 600, // 10 minutes
    effects: [
      { target: 'all_currency_value', modifier: 0.7, duration: 600 },
      { target: 'mining_difficulty', modifier: 0.8, duration: 600 },
    ],
  },
  'network_upgrade': {
    name: 'Network Upgrade',
    type: 'POSITIVE' as const,
    description: 'A major blockchain upgrade increases mining efficiency!',
    probability: 0.08,
    duration: 180, // 3 minutes
    effects: [
      { target: 'mining_output', modifier: 1.3, duration: 180 },
    ],
  },
  'regulation_news': {
    name: 'Regulatory Uncertainty',
    type: 'NEGATIVE' as const,
    description: 'Government regulations create market uncertainty.',
    probability: 0.06,
    duration: 900, // 15 minutes
    effects: [
      { target: 'market_volatility', modifier: 1.5, duration: 900 },
      { target: 'trading_confidence', modifier: 0.8, duration: 900 },
    ],
  },
  'whale_movement': {
    name: 'Whale Activity',
    type: 'NEUTRAL' as const,
    description: 'Large cryptocurrency holders are making moves.',
    probability: 0.1,
    duration: 120, // 2 minutes
    effects: [
      { target: 'market_volatility', modifier: 1.2, duration: 120 },
      { target: 'shard_drop_rate', modifier: 1.1, duration: 120 },
    ],
  },
} as const;

// UI Constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  NOTIFICATION_DURATION: 5000,
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  MARKET_CHART_POINTS: 50,
  LEADERBOARD_SIZE: 100,
} as const;

// Format helpers
export const formatCurrency = (amount: number, currency: CurrencyType): string => {
  const config = CURRENCY_CONFIGS[currency];
  if (amount >= 1e12) return `${(amount / 1e12).toFixed(2)}T ${config.symbol}`;
  if (amount >= 1e9) return `${(amount / 1e9).toFixed(2)}B ${config.symbol}`;
  if (amount >= 1e6) return `${(amount / 1e6).toFixed(2)}M ${config.symbol}`;
  if (amount >= 1e3) return `${(amount / 1e3).toFixed(2)}K ${config.symbol}`;
  return `${amount.toFixed(2)} ${config.symbol}`;
};

export const formatNumber = (num: number): string => {
  if (num >= 1e12) return `${(num / 1e12).toFixed(2)}T`;
  if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
  if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
  if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
  return num.toFixed(2);
};