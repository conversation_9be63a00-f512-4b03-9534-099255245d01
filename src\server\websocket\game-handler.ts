import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
// import { getServerAuthSession } from '~/server/auth'; // TODO: Fix auth integration
import { db } from '~/server/db';
import { GAME_CONSTANTS } from '~/config/game-constants';
import { calculateClickPower, decimalToNumber } from '~/lib/game-utils';
import { verifyWebSocketToken } from '~/lib/websocket-auth';

interface ClickData {
  timestamp: number;
  clickPower?: number;
}

interface UserSession {
  userId: string;
  socketId: string;
  lastClickTime: number;
  clickCount: number;
  joinedAt: number;
}

class GameWebSocketHandler {
  private io: SocketIOServer;
  private userSessions = new Map<string, UserSession>();
  private clickQueue = new Map<string, ClickData[]>();
  
  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      path: '/api/socket',
      cors: {
        origin: process.env.NEXTAUTH_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
      },
    });

    this.setupNamespace();
    this.startClickProcessor();
  }

  private setupNamespace() {
    const gameNamespace = this.io.of('/game');

    gameNamespace.use(async (socket, next) => {
      try {
        const { userId, token } = socket.handshake.auth || {};
        console.log(`🔐 WebSocket auth attempt - UserId: ${userId}, Token: ${token ? 'Present' : 'Missing'}`);

        if (!userId || userId === 'anonymous') {
          // Generate a temporary user ID for anonymous users
          const tempUserId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          socket.data.userId = tempUserId;
          socket.data.isAuthenticated = false;
          console.log(`🔧 Generated temporary user ID: ${tempUserId}`);
          next();
          return;
        }

        // If we have a token, verify it
        if (token) {
          console.log(`🔐 Verifying token for user: ${userId}`);
          const tokenPayload = verifyWebSocketToken(token);
          if (tokenPayload && tokenPayload.userId === userId) {
            socket.data.userId = userId;
            socket.data.isAuthenticated = true;
            console.log(`✅ Successfully authenticated user: ${userId}`);
            next();
            return;
          } else {
            console.warn(`🚫 Invalid token for user: ${userId}. Token payload:`, tokenPayload);
            // Don't reject, just mark as unauthenticated
            socket.data.userId = userId;
            socket.data.isAuthenticated = false;
            console.log(`⚠️ Token verification failed, connecting as unauthenticated: ${userId}`);
            next();
            return;
          }
        }

        // Check if this user exists in the database (fallback authentication)
        try {
          const player = await db.player.findUnique({
            where: { userId },
            select: { id: true, userId: true }
          });

          if (player) {
            socket.data.userId = userId;
            socket.data.isAuthenticated = true; // Consider them authenticated if they have a player record
            console.log(`✅ User verified via database: ${userId}`);
            next();
            return;
          }
        } catch (dbError) {
          console.warn(`⚠️ Database check failed for user: ${userId}`, dbError);
        }

        // Fallback: allow connection but mark as unauthenticated
        socket.data.userId = userId;
        socket.data.isAuthenticated = false;
        console.log(`⚠️ Unauthenticated connection for user: ${userId} (no token, not in database)`);
        next();
      } catch (error) {
        console.error('❌ WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    gameNamespace.on('connection', (socket) => {
      const userId = socket.data.userId;
      console.log(`🎮 User ${userId} connected to game WebSocket`);

      // Register user session
      this.userSessions.set(userId, {
        userId,
        socketId: socket.id,
        lastClickTime: 0,
        clickCount: 0,
        joinedAt: Date.now(),
      });

      // Initialize click queue
      this.clickQueue.set(userId, []);

      socket.on('join_game', async (data) => {
        try {
          await this.handleJoinGame(socket, data);
        } catch (error) {
          socket.emit('error', { message: 'Failed to join game' });
        }
      });

      socket.on('click', (data: ClickData) => {
        this.handleClick(socket, data);
      });

      socket.on('disconnect', () => {
        console.log(`🎮 User ${userId} disconnected from game WebSocket`);
        this.userSessions.delete(userId);
        this.clickQueue.delete(userId);
      });
    });
  }

  private async handleJoinGame(socket: any, data: { userId: string }) {
    const userId = socket.data.userId;
    console.log(`🎮 Handling join_game for user: ${userId}`);
    
    try {
      // Send initial game state
      const player = await db.player.findUnique({
        where: { userId },
        include: {
          miningRigs: true,
          shards: {
            where: { equipped: true },
            include: { shard: true }
          }
        }
      });

      if (player) {
        const balance = decimalToNumber(player.cryptoCoin);
        const totalClicks = Number(player.totalClicks);
        
        // Calculate CPS
        const cps = player.miningRigs.reduce((total, rig) => {
          return total + (Number(rig.baseOutput) * rig.quantity * Number(rig.efficiency));
        }, 0);

        console.log(`📊 Sending game state - Balance: ${balance}, Clicks: ${totalClicks}, CPS: ${cps}`);
        
        socket.emit('game_update', {
          balance,
          totalClicks,
          cps,
        });
      } else {
        // For temporary users, send default state
        console.log(`👤 No player found for ${userId}, sending default state`);
        socket.emit('game_update', {
          balance: 10, // Starting balance
          totalClicks: 0,
          cps: 0,
        });
      }
    } catch (error) {
      console.error(`❌ Error in handleJoinGame for ${userId}:`, error);
      socket.emit('error', { message: 'Failed to load game state' });
    }
  }

  private handleClick(socket: any, data: ClickData) {
    const userId = socket.data.userId;
    const session = this.userSessions.get(userId);
    
    if (!session) {
      socket.emit('error', { message: 'Session not found' });
      return;
    }

    const now = Date.now();
    
    // Anti-cheat: Minimum 100ms between clicks
    if (now - session.lastClickTime < 100) {
      socket.emit('rate_limit_warning', { 
        message: 'Clicking too fast! Minimum 100ms between clicks.' 
      });
      return;
    }

    // Anti-cheat: Maximum clicks per second
    if (session.clickCount >= 10) {
      const timeSinceJoin = now - session.joinedAt;
      if (timeSinceJoin < 1000) {
        socket.emit('rate_limit_warning', { 
          message: 'Too many clicks per second!' 
        });
        return;
      }
      // Reset counter every second
      session.clickCount = 0;
      session.joinedAt = now;
    }

    // Update session
    session.lastClickTime = now;
    session.clickCount++;

    // Add click to processing queue
    const userQueue = this.clickQueue.get(userId) || [];
    userQueue.push({
      timestamp: data.timestamp,
      clickPower: GAME_CONSTANTS.BASE_CLICK_POWER,
    });
    this.clickQueue.set(userId, userQueue);
  }

  private startClickProcessor() {
    // Process clicks every 100ms with built-in delay for anti-cheat
    setInterval(async () => {
      for (const [userId, clicks] of this.clickQueue.entries()) {
        if (clicks.length === 0) continue;

        try {
          await this.processUserClicks(userId, clicks);
          this.clickQueue.set(userId, []); // Clear processed clicks
        } catch (error) {
          console.error(`Failed to process clicks for user ${userId}:`, error);
        }
      }
    }, 100); // 100ms processing interval = built-in anti-cheat delay
  }

  private async processUserClicks(userId: string, clicks: ClickData[]) {
    const session = this.userSessions.get(userId);
    if (!session) return;

    const socket = this.io.of('/game').sockets.get(session.socketId);
    if (!socket) return;

    // Get player with shards for bonus calculation
    const player = await db.player.findUnique({
      where: { userId },
      include: {
        shards: {
          where: { equipped: true },
          include: { shard: true }
        }
      }
    });

    if (!player) {
      socket.emit('error', { message: 'Player not found' });
      return;
    }

    // Calculate total earnings from all clicks
    const totalClicks = clicks.length;
    const clickPower = calculateClickPower(player.shards as any, GAME_CONSTANTS.BASE_CLICK_POWER);
    const totalEarnings = clickPower * totalClicks;

    // Update player in database
    const updatedPlayer = await db.player.update({
      where: { userId },
      data: {
        cryptoCoin: {
          increment: totalEarnings
        },
        totalClicks: {
          increment: totalClicks
        },
        lastActiveAt: new Date(),
      },
    });

    const newBalance = decimalToNumber(updatedPlayer.cryptoCoin);
    const newClickCount = Number(updatedPlayer.totalClicks);

    // Send acknowledgment for each click (for UI feedback)
    clicks.forEach(() => {
      socket.emit('click_acknowledged', {
        earnedAmount: clickPower,
        newBalance,
      });
    });

    // Send updated game state
    socket.emit('game_update', {
      balance: newBalance,
      totalClicks: newClickCount,
      cps: 0, // Will be calculated separately
    });
  }

  // Method to broadcast game updates (for passive income, purchases, etc.)
  public broadcastGameUpdate(userId: string, data: {
    balance?: number;
    totalClicks?: number;
    cps?: number;
    passiveIncome?: number;
  }) {
    const session = this.userSessions.get(userId);
    if (!session) return;

    const socket = this.io.of('/game').sockets.get(session.socketId);
    if (socket) {
      socket.emit('game_update', data);
    }
  }

  public getConnectedUsers(): string[] {
    return Array.from(this.userSessions.keys());
  }

  public getAuthenticatedUsers(): string[] {
    const authenticatedUsers: string[] = [];

    for (const [userId, session] of this.userSessions.entries()) {
      const socket = this.io.of('/game').sockets.get(session.socketId);
      if (socket?.data?.isAuthenticated && !userId.startsWith('temp_')) {
        authenticatedUsers.push(userId);
      }
    }

    return authenticatedUsers;
  }
}

let gameHandler: GameWebSocketHandler | null = null;

export function initializeGameWebSocket(server: HTTPServer): GameWebSocketHandler {
  if (!gameHandler) {
    gameHandler = new GameWebSocketHandler(server);
  }
  return gameHandler;
}

export function getGameWebSocketHandler(): GameWebSocketHandler | null {
  return gameHandler;
}