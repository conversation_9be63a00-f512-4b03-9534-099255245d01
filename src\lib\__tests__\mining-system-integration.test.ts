import { describe, test, expect } from 'vitest';
import { 
  calculateTotalCPS,
  calculateRigCost,
  calculateUpgradeCost,
  calculateOfflineProduction,
  calculateClickPower,
  applyShardBonuses,
  validateClickRate,
  validateClickPattern,
  decimalToNumber,
} from '../game-utils';
import { RigType, ShardRarity, ShardCategory } from '../../types/game';
import type { MiningRig, PlayerShard, BlockchainShard } from '../../types/game';
import { Decimal } from '@prisma/client/runtime/library';
import { GAME_CONSTANTS } from '../../config/game-constants';

describe('Mining System Integration Tests', () => {
  // Mock data for comprehensive testing
  const mockShards: BlockchainShard[] = [
    {
      id: 'shard1',
      name: 'Mining Boost Shard',
      rarity: ShardRarity.RARE,
      category: ShardCategory.MINING_BOOST,
      effects: [
        { type: 'MULTIPLIER', target: 'mining_output', value: 1.5 },
        { type: 'FLAT_BONUS', target: 'click_power', value: 2 },
      ],
      description: 'Increases mining output by 50%',
    },
    {
      id: 'shard2',
      name: 'Efficiency Shard',
      rarity: ShardRarity.EPIC,
      category: ShardCategory.EFFICIENCY,
      effects: [
        { type: 'MULTIPLIER', target: 'offline_production', value: 1.2 },
        { type: 'FLAT_BONUS', target: 'mining_output', value: 5 },
      ],
      description: 'Improves offline production efficiency',
    },
  ];

  const mockPlayerShards: PlayerShard[] = [
    {
      id: 'ps1',
      playerId: 'player1',
      shardId: 'shard1',
      shard: mockShards[0],
      quantity: 2,
      equipped: true,
      acquiredAt: new Date(),
    },
    {
      id: 'ps2',
      playerId: 'player1',
      shardId: 'shard2',
      shard: mockShards[1],
      quantity: 1,
      equipped: true,
      acquiredAt: new Date(),
    },
  ];

  describe('Complete Mining Progression Scenario', () => {
    test('should simulate complete player progression from clicking to automated mining', () => {
      // Phase 1: Initial clicking phase
      const baseClickPower = GAME_CONSTANTS.BASE_CLICK_POWER;
      const enhancedClickPower = calculateClickPower(mockPlayerShards, baseClickPower);
      
      // Player gets 2 flat bonus from shard1 (quantity 2) = 4 total
      // Base click power: 1, Enhanced: 1 + 4 = 5
      expect(enhancedClickPower).toBe(5);

      // Phase 2: First rig purchase
      const firstRigCost = calculateRigCost(RigType.USB_ASIC, 0, 1);
      expect(firstRigCost).toBe(10); // Base cost for first USB_ASIC

      // Phase 3: Building up mining rigs
      const miningRigs: MiningRig[] = [
        {
          id: '1',
          playerId: 'player1',
          rigType: RigType.USB_ASIC,
          level: 3,
          quantity: 10,
          baseOutput: new Decimal(0.1),
          efficiency: new Decimal(1.0),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          playerId: 'player1',
          rigType: RigType.GPU_FARM,
          level: 2,
          quantity: 5,
          baseOutput: new Decimal(1.0),
          efficiency: new Decimal(1.0),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Calculate base CPS
      const baseCPS = calculateTotalCPS(miningRigs);
      // USB_ASIC: 0.1 * 1.0 * 3 * 10 = 3.0
      // GPU_FARM: 1.0 * 1.0 * 2 * 5 = 10.0
      // Total: 13.0
      expect(baseCPS).toBe(13.0);

      // Apply shard bonuses to mining output
      const bonusedCPS = applyShardBonuses(baseCPS, mockPlayerShards, 'mining_output');
      // Base: 13.0
      // Flat bonus from shard2: +5 (quantity 1) = +5
      // Multiplier from shard1 (quantity 2): 1.5^2 = 2.25
      // Result: (13.0 + 5) * 2.25 = 40.5
      expect(bonusedCPS).toBe(40.5);

      // Phase 4: Offline production calculation
      const offlineTimeSeconds = 8 * 3600; // 8 hours (at efficiency threshold)
      const offlineProduction = calculateOfflineProduction(miningRigs, mockPlayerShards, offlineTimeSeconds);
      
      // Let's calculate the actual expected value step by step:
      // 1. Base CPS from rigs: 13.0
      // 2. Apply offline_production bonuses from shards (shard2 has offline_production bonus of 1.2x)
      // 3. The offline production calculation applies bonuses to the base CPS, not the mining_output bonuses
      // 4. So offline CPS = applyShardBonuses(13.0, shards, 'offline_production') = 13.0 * 1.2 = 15.6
      // 5. Time: 8 hours = 28800 seconds, Efficiency: 1.0 (no penalty for 8 hours)
      // 6. Total: 15.6 * 28800 * 1.0 = 449,280
      expect(offlineProduction).toBeCloseTo(449280, 0);

      // Phase 5: Long offline period with efficiency penalty
      const longOfflineTimeSeconds = 16 * 3600; // 16 hours
      const longOfflineProduction = calculateOfflineProduction(miningRigs, mockPlayerShards, longOfflineTimeSeconds);
      
      // Same calculation but with efficiency penalty (0.8) for > 8 hours
      // Offline CPS: 15.6 (same as above)
      // Time: 16 hours = 57600 seconds, Efficiency: 0.8 (penalty for > 8 hours)
      // Total: 15.6 * 57600 * 0.8 = 718,848
      expect(longOfflineProduction).toBeCloseTo(718848, 0);
    });

    test('should handle rig upgrade progression correctly', () => {
      // Start with level 1 rig
      const initialLevel = 1;
      const upgradeCount = 5;
      
      // Calculate cost to upgrade 5 levels
      const upgradeCost = calculateUpgradeCost(RigType.USB_ASIC, initialLevel, upgradeCount);
      
      // Level 1->2: 10 * 1.5^1 = 15
      // Level 2->3: 10 * 1.5^2 = 22.5
      // Level 3->4: 10 * 1.5^3 = 33.75
      // Level 4->5: 10 * 1.5^4 = 50.625
      // Level 5->6: 10 * 1.5^5 = 75.9375
      // Total: 197.8125
      expect(upgradeCost).toBeCloseTo(197.8125, 4);

      // Verify that higher level rigs produce more
      const lowLevelRig: MiningRig = {
        id: '1',
        playerId: 'player1',
        rigType: RigType.USB_ASIC,
        level: 1,
        quantity: 1,
        baseOutput: new Decimal(0.1),
        efficiency: new Decimal(1.0),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const highLevelRig: MiningRig = {
        ...lowLevelRig,
        id: '2',
        level: 6,
      };

      const lowLevelCPS = calculateTotalCPS([lowLevelRig]);
      const highLevelCPS = calculateTotalCPS([highLevelRig]);

      expect(lowLevelCPS).toBe(0.1); // 0.1 * 1.0 * 1 * 1
      expect(highLevelCPS).toBeCloseTo(0.6, 10); // 0.1 * 1.0 * 6 * 1
    });
  });

  describe('Rate Limiting Integration', () => {
    test('should properly validate realistic clicking scenarios', () => {
      // Simulate a realistic gaming session
      const now = Date.now();
      const realisticClicks: number[] = [];
      
      // Generate clicks with human-like timing
      let currentTime = now - 30000; // Start 30 seconds ago
      for (let i = 0; i < 50; i++) {
        // Random interval between 100-800ms (human-like)
        const interval = 100 + Math.random() * 700;
        currentTime += interval;
        realisticClicks.push(currentTime);
      }

      expect(validateClickRate(realisticClicks)).toBe(true);
      expect(validateClickPattern(realisticClicks.slice(-10))).toBe(true);
    });

    test('should detect and reject bot-like behavior', () => {
      const now = Date.now();
      
      // Generate perfectly timed clicks (bot behavior)
      const botClicks = Array.from({ length: 30 }, (_, i) => now - i * 50);
      
      expect(validateClickPattern(botClicks.slice(-10))).toBe(false);
    });

    test('should handle burst clicking appropriately', () => {
      const now = Date.now();
      
      // Generate a burst of clicks followed by normal clicking
      const burstClicks: number[] = [];
      
      // Initial burst (10 clicks in 200ms)
      for (let i = 0; i < 10; i++) {
        burstClicks.push(now - 1000 + i * 20);
      }
      
      // Normal clicking after burst
      for (let i = 0; i < 10; i++) {
        burstClicks.push(now - 800 + i * 150);
      }

      // Should fail pattern validation due to initial burst
      expect(validateClickPattern(burstClicks)).toBe(false);
      
      // But rate limiting might still pass if total count is within limits
      const recentClicks = burstClicks.filter(click => now - click < GAME_CONSTANTS.CLICK_RATE_WINDOW);
      expect(validateClickRate(recentClicks)).toBe(recentClicks.length <= GAME_CONSTANTS.CLICK_RATE_LIMIT);
    });
  });

  describe('Economic Balance Validation', () => {
    test('should maintain reasonable economic progression', () => {
      // Test that costs scale appropriately to prevent runaway inflation
      const rigCosts = [];
      for (let quantity = 0; quantity < 20; quantity++) {
        rigCosts.push(calculateRigCost(RigType.USB_ASIC, quantity, 1));
      }

      // Verify exponential growth
      for (let i = 1; i < rigCosts.length; i++) {
        expect(rigCosts[i]!).toBeGreaterThan(rigCosts[i - 1]!);
        
        // Growth rate should be consistent with multiplier (1.15)
        const growthRate = rigCosts[i]! / rigCosts[i - 1]!;
        expect(growthRate).toBeCloseTo(1.15, 2);
      }
    });

    test('should validate shard effect stacking', () => {
      // Test that multiple shards stack appropriately
      const multipleShards: PlayerShard[] = [
        {
          id: 'ps1',
          playerId: 'player1',
          shardId: 'shard1',
          shard: {
            id: 'shard1',
            name: 'Multiplier Shard 1',
            rarity: ShardRarity.COMMON,
            category: ShardCategory.MINING_BOOST,
            effects: [{ type: 'MULTIPLIER', target: 'mining_output', value: 1.1 }],
            description: 'Small mining boost',
          },
          quantity: 1,
          equipped: true,
          acquiredAt: new Date(),
        },
        {
          id: 'ps2',
          playerId: 'player1',
          shardId: 'shard2',
          shard: {
            id: 'shard2',
            name: 'Multiplier Shard 2',
            rarity: ShardRarity.COMMON,
            category: ShardCategory.MINING_BOOST,
            effects: [{ type: 'MULTIPLIER', target: 'mining_output', value: 1.2 }],
            description: 'Medium mining boost',
          },
          quantity: 1,
          equipped: true,
          acquiredAt: new Date(),
        },
      ];

      const baseValue = 100;
      const result = applyShardBonuses(baseValue, multipleShards, 'mining_output');
      
      // Should multiply: 100 * 1.1 * 1.2 = 132
      expect(result).toBe(132);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle extreme values gracefully', () => {
      // Test with very large numbers
      const extremeRig: MiningRig = {
        id: '1',
        playerId: 'player1',
        rigType: RigType.DYSON_SPHERE,
        level: 100,
        quantity: 1000,
        baseOutput: new Decimal(1400),
        efficiency: new Decimal(2.0),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const extremeCPS = calculateTotalCPS([extremeRig]);
      expect(extremeCPS).toBe(280000000); // 1400 * 2.0 * 100 * 1000
      expect(isFinite(extremeCPS)).toBe(true);
    });

    test('should handle zero and negative values appropriately', () => {
      const zeroRig: MiningRig = {
        id: '1',
        playerId: 'player1',
        rigType: RigType.USB_ASIC,
        level: 0,
        quantity: 0,
        baseOutput: new Decimal(0),
        efficiency: new Decimal(0),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const zeroCPS = calculateTotalCPS([zeroRig]);
      expect(zeroCPS).toBe(0);

      // Test offline production with zero CPS
      const zeroOfflineProduction = calculateOfflineProduction([zeroRig], [], 3600);
      expect(zeroOfflineProduction).toBe(0);
    });

    test('should handle invalid shard data gracefully', () => {
      const invalidShard: PlayerShard = {
        id: 'ps1',
        playerId: 'player1',
        shardId: 'shard1',
        shard: undefined as any,
        quantity: 1,
        equipped: true,
        acquiredAt: new Date(),
      };

      const baseValue = 100;
      const result = applyShardBonuses(baseValue, [invalidShard], 'mining_output');
      
      // Should return base value when shard is invalid
      expect(result).toBe(100);
    });
  });
});